import json
import asyncio
import os
import wave
import datetime
import logging
from fastapi import FastAPI, WebSocket
from fastapi.websockets import WebSocketDisconnect
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


logger = logging.getLogger(__name__)

# --- FastAPI Application Instance ---
app = FastAPI()

# Constants
SAMPLE_RATE = 8000               # 8kHz
CHUNK_DURATION_SEC = 1           # Process every 1 second
BYTES_PER_SAMPLE = 2             # PCM16 = 2 bytes per sample
CHUNK_SIZE = SAMPLE_RATE * BYTES_PER_SAMPLE * CHUNK_DURATION_SEC  # 16000 bytes

# Queue for async processing
audio_queue = asyncio.Queue()

# Optional: Save to file for debugging
def save_to_wav(pcm_bytes, sample_rate=SAMPLE_RATE, file_prefix="audio_chunk"):
    timestamp = datetime.datetime.now(datetime.timezone.utc).strftime("%Y%m%d_%H%M%S%f")
    filename = f"{file_prefix}_{timestamp}.wav"
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)            # Mono
        wf.setsampwidth(2)            # 16-bit
        wf.setframerate(sample_rate)
        wf.writeframes(pcm_bytes)
    print(f"Saved: {filename}")

# Async consumer that processes audio chunks
async def audio_consumer():
    buffer = b""
    while True:
        audio_bytes = await audio_queue.get()
        buffer += audio_bytes

        # If buffer large enough, send to model
        if len(buffer) >= CHUNK_SIZE:
            chunk = buffer[:CHUNK_SIZE]
            buffer = buffer[CHUNK_SIZE:]

            # Optional: Save for verification
            save_to_wav(chunk)

            # Call your inference model here (OpenAI, Whisper, etc.)
            # Example: result = await openai_streaming_model(chunk)
            print("Processed 1 second of audio")

# Configuration from environment variables
WEBSOCKET_HOST = os.getenv('WEBSOCKET_HOST', '0.0.0.0')
WEBSOCKET_PORT = int(os.getenv('WEBSOCKET_PORT', '8088'))
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'audio.raw')

# Ensure output file is fresh
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

@app.get("/")
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}


@app.websocket("/rtp-stream")
async def audio_handler(websocket: WebSocket):
    """
    Handler for incoming WebSocket connections.
    Receives binary audio frames and writes them to a raw file.
    """
    await websocket.accept()
    print(f"Client connected: {websocket.client}")
    try:
        while True:
            # Receive any type of message
            message = await websocket.receive()

            # Handle different message types
            if message["type"] == "websocket.receive":
                if "bytes" in message:
                    # Binary message (audio data)
                    audio_data = message["bytes"]
                    print(f"Received {len(audio_data)} bytes of audio")
                    with open(OUTPUT_FILE, 'ab') as f:
                        f.write(audio_data)
                    # Send acknowledgment
                    await websocket.send_text(f"Received {len(audio_data)} bytes")

                elif "text" in message:
                    # Text message
                    text_data = message["text"]
                    print(f"Received text message: {text_data}")
                    # Echo the message back
                    await websocket.send_text(f"Echo: {text_data}")

            elif message["type"] == "websocket.disconnect":
                print("Client disconnected")
                break

    except WebSocketDisconnect:
        print("WebSocket connection closed gracefully")
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        print("WebSocket handler finished")

# Remove the standalone WebSocket server - use FastAPI instead
# Run with: uvicorn test:app --host 0.0.0.0 --port 8088

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=WEBSOCKET_HOST, port=WEBSOCKET_PORT)